from django.conf import settings
from django.contrib.auth.backends import Base<PERSON>ackend

from rest_framework.authentication import BaseAuthentication
from rest_framework import permissions

from mc2.mastercom.next_api import NextApi
from .utils import handle_user_from_next_api

class NextApiAuthentication(BaseAuthentication):

    def authenticate(self, request):
        token = request.META.get('HTTP_AUTHORIZATION')

        if not token:
            return None, None
        next_api = NextApi()
        user_data = next_api.verify(token)
        if user_data:
            return (handle_user_from_next_api(user_data), token)
        return None, token


class IsLocalhost(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.META['REMOTE_ADDR'] in settings.AUTH_ALLOWED_HOSTS
    

class NextApiBackend(BaseBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        next_api = NextApi()
        user_data = next_api.verify(password)
        if user_data:
            return handle_user_from_next_api(user_data)
        return None